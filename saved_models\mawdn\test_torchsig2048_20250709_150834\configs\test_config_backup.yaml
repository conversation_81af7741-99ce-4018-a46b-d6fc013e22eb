data:
  dataset_type: torchsig2048
  label_mapping: true
  modulations: null
  normalize: true
  rml201801a_file_path: data/GOLD_XYZ_OSC.0001_1024.hdf5
  rml201801a_modulations: null
  rml201801a_use_all_snr: true
  rml_file_path: data/RML2016.10a_dict.pkl
  samples_per_key: null
  sequence_lengths:
    hisar: 1024
    rml: 128
    rml201801a: 1024
    torchsig1024: 1024
    torchsig2048: 2048
    torchsig4096: 4096
  snr_range:
  - -20
  - 18
  snr_ranges:
    hisar:
    - -20
    - 18
    rml:
    - -20
    - 18
    rml201801a:
    - -20
    - 30
    torchsig1024:
    - 0
    - 30
    torchsig2048:
    - 0
    - 30
    torchsig4096:
    - 0
    - 30
  stratify_by_snr: true
  test_labels_path: data/hisar/test_labels.csv
  test_path: data/hisar/test_data.mat
  test_ratio: 0.15
  test_snr_path: data/hisar/test_snr.csv
  torchsig1024_test_labels_path: data/torchsig1024/test_labels.csv
  torchsig1024_test_path: data/torchsig1024/test_data.mat
  torchsig1024_test_snr_path: data/torchsig1024/test_snr.csv
  torchsig1024_train_labels_path: data/torchsig1024/train_labels.csv
  torchsig1024_train_path: data/torchsig1024/train_data.mat
  torchsig1024_train_snr_path: data/torchsig1024/train_snr.csv
  torchsig2048_test_labels_path: data/torchsig2048/test_labels.csv
  torchsig2048_test_path: data/torchsig2048/test_data.mat
  torchsig2048_test_snr_path: data/torchsig2048/test_snr.csv
  torchsig2048_train_labels_path: data/torchsig2048/train_labels.csv
  torchsig2048_train_path: data/torchsig2048/train_data.mat
  torchsig2048_train_snr_path: data/torchsig2048/train_snr.csv
  torchsig4096_test_labels_path: data/torchsig4096/test_labels.csv
  torchsig4096_test_path: data/torchsig4096/test_data.mat
  torchsig4096_test_snr_path: data/torchsig4096/test_snr.csv
  torchsig4096_train_labels_path: data/torchsig4096/train_labels.csv
  torchsig4096_train_path: data/torchsig4096/train_data.mat
  torchsig4096_train_snr_path: data/torchsig4096/train_snr.csv
  train_labels_path: data/hisar/train_labels.csv
  train_path: data/hisar/train_data.mat
  train_ratio: 0.7
  train_snr_path: data/hisar/train_snr.csv
  val_ratio: 0.15
hisar_class_names:
- BPSK
- QPSK
- 8PSK
- 16PSK
- 32PSK
- 64PSK
- 4QAM
- 8QAM
- 16QAM
- 32QAM
- 64QAM
- 128QAM
- 256QAM
- 2FSK
- 4FSK
- 8FSK
- 16FSK
- 4PAM
- 8PAM
- 16PAM
- AM-DSB
- AM-DSB-SC
- AM-USB
- AM-LSB
- FM
- PM
model:
  decomposition_levels: 4
  feature_channels: 16
  in_channels: 2
  mid_channels: 200
  name: MAWDN
  num_classes: 26
  sequence_length: 1024
output_dir: ./saved_models/mawdn
rml201801a_class_names:
- OOK
- 4ASK
- 8ASK
- BPSK
- QPSK
- 8PSK
- 16PSK
- 32PSK
- 16APSK
- 32APSK
- 64APSK
- 128APSK
- 16QAM
- 32QAM
- 64QAM
- 128QAM
- 256QAM
- AM-SSB-WC
- AM-SSB-SC
- AM-DSB-WC
- AM-DSB-SC
- FM
- GMSK
- OQPSK
rml_class_names:
- 8PSK
- AM-DSB
- AM-SSB
- BPSK
- CPFSK
- GFSK
- PAM4
- QAM16
- QAM64
- QPSK
- WBFM
torchsig_class_names:
- BPSK
- QPSK
- 8PSK
- 16PSK
- 32PSK
- 64PSK
- 16QAM
- 32QAM
- 64QAM
- 256QAM
- 2FSK
- 4FSK
- 8FSK
- 16FSK
- 4ASK
- 8ASK
- 16ASK
- 32ASK
- 64ASK
- AM-DSB
- AM-DSB-SC
- AM-USB
- AM-LSB
- FM
- OOK
training:
  batch_size: 64
  device: cuda
  early_stop_patience: 10
  early_stopping: true
  epochs: 100
  lambda_lifting: 0.01
  learning_rate: 0.001
  min_delta: 0.001
  monitor: val_acc
  num_workers: 12
  patience: 10
  persistent_workers: true
  pin_memory: true
  prefetch_factor: 8
  seed: 42
  weight_decay: 1.0e-05
